<template>
  <div ref="chartRef" id="averageChart" :style="{ width, height }"></div>
</template>
<script setup lang="ts">
  import { watch, ref, Ref } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';

  const props = defineProps({
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    },
    data: {
      type: Array as PropType<any>,
      default: () => [],
    },
  });
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  watch(
    () => props.data,
    (newVal) => {
      if (newVal) {
        setOptions({
          title: {
            text: '绩效平均分',
          },
          radar: [
            {
              indicator: newVal.indicator,
              radius: 80, // 缩放
              // center: ['50%', '50%'], // 位置
              axisName: {
                fontSize: 10,
                color: '#2F3133',
                formatter: (value) => {
                  let zR;
                  let jR;
                  console.log(newVal.valueType);

                  newVal.valueType.map((item) => {
                    if (item.name == value) {
                      zR = item.zR;
                      jR = item.jR;
                    }
                  });
                  return `${value}\n{a|${zR}}/{b|${jR}}`;
                },
                rich: {
                  a: {
                    color: '#FF8C20',
                    fontSize: 10,
                  },
                  b: {
                    color: '#FFCF90',
                    fontSize: 10,
                  },
                },
              },
            },
          ],
          legend: {
            show: true,
            orient: 'horizontal',
            right: '1%',
            bottom: '3%',
            icon: 'circle',
          },
          tooltip: {
            trigger: 'item',
            show: false,
          },
          series: [
            {
              data: newVal.data,
              type: 'radar',
              tooltip: {
                trigger: 'item',
              },
              itemStyle: {
                // 修改 color 函数返回类型为 ZRColor 支持的类型
                color: (params: any): string => {
                  const colors = ['#f39423', '#f9c78b'];
                  return colors[params.dataIndex % colors.length];
                },
              },
            },
          ] as any,
        });
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped lang="less"></style>
